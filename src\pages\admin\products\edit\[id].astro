---
import AdminLayout from '../../../../layouts/AdminLayout.astro';
import { isAuthenticated } from '../../../../utils/auth';

// Защита страницы
if (!isAuthenticated(Astro.request)) {
  return Astro.redirect('/admin/login');
}

// Получение ID товара из параметров URL
const { id } = Astro.params;

// Загрузка данных о товарах
import productsData from '../../../../../data/product/products.json';

// Поиск товара по ID
const product = productsData.find(p => p.id === id);

// Если товар не найден, перенаправляем на список товаров
if (!product) {
  return Astro.redirect('/admin/products');
}
---

<AdminLayout title={`Редактирование товара ${product.name} | LuxBeton`}>
  <div class="container mx-auto py-8 px-4">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">Редактирование товара</h1>
      <a href="/admin/products" class="bg-gray-300 text-gray-800 px-4 py-2 rounded hover:bg-gray-400 inline-flex items-center">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
        </svg>
        Назад к списку товаров
      </a>
    </div>

    <form id="product-form" class="bg-white rounded-lg shadow-md p-6">
      <input type="hidden" id="product-id" value={product.id}>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Основная информация -->
        <div>
          <h2 class="text-xl font-semibold mb-4">Основная информация</h2>

          <div class="mb-4">
            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Название товара</label>
            <input
              type="text"
              id="name"
              value={product.name}
              class="w-full px-3 py-2 border border-gray-300 rounded-md"
              required
            >
          </div>

          <div class="mb-4">
            <div class="relative">
              <label for="category" class="block text-sm font-medium text-gray-700 mb-1">
                Категория
                <svg
                  class="w-4 h-4 text-yellow-600 cursor-help inline-block ml-2 category-info-icon"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </label>
              <input
                type="text"
                id="category"
                value={product.category}
                class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 cursor-not-allowed category-input"
                readonly
                disabled
              >
              <!-- Tooltip -->
              <div
                id="category-warning-tooltip"
                class="category-tooltip absolute bottom-full left-0 mb-2 px-3 py-2 bg-yellow-50 border border-yellow-200 rounded shadow-lg opacity-0 pointer-events-none transition-opacity duration-200 z-10 w-80"
              >
                <div class="text-sm font-medium text-yellow-800 mb-1">Изменение категории недоступно</div>
                <div class="text-xs text-yellow-700">Категория влияет на ID товара. Для изменения категории создайте новый товар в нужной категории.</div>
                <!-- Стрелка tooltip'а -->
                <div class="absolute top-full left-4 border-4 border-transparent border-t-yellow-200"></div>
              </div>
            </div>
          </div>

          <div class="mb-4">
            <label for="subcategory" class="block text-sm font-medium text-gray-700 mb-1">Подкатегория</label>
            <select
              id="subcategory"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Загрузка подкатегорий...</option>
            </select>
          </div>

          <div class="mb-4">
            <label for="product-slug" class="block text-sm font-medium text-gray-700 mb-1">SLUG (URL товара)</label>
            <div class="flex items-center space-x-2">
              <input
                type="text"
                id="product-slug"
                value={product.slug || ''}
                placeholder="url-slug-tovara"
                class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
              >
              <button
                type="button"
                id="generate-product-slug-btn"
                class="generate-product-slug-btn px-3 py-2 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm whitespace-nowrap"
                style="background-color: #3b82f6;"
                title="Генерировать SLUG из названия товара с транслитерацией"
              >
                Генерировать
              </button>
            </div>
            <p class="mt-1 text-xs text-gray-500">Автоматически генерируется из названия или нажмите кнопку для ручной генерации</p>
          </div>

          <div class="mb-4">
            <label for="shortDescription" class="block text-sm font-medium text-gray-700 mb-1">Краткое описание</label>
            <textarea
              id="shortDescription"
              class="w-full px-3 py-2 border border-gray-300 rounded-md"
              rows="2"
              required
            >{product.shortDescription}</textarea>
          </div>

          <div class="mb-4">
            <label for="fullDescription" class="block text-sm font-medium text-gray-700 mb-1">Полное описание</label>
            <textarea
              id="fullDescription"
              class="w-full px-3 py-2 border border-gray-300 rounded-md"
              rows="5"
            >{product.fullDescription}</textarea>
          </div>
        </div>

        <!-- Цена и атрибуты -->
        <div>
          <h2 class="text-xl font-semibold mb-4">Цена и атрибуты</h2>

          <div class="grid grid-cols-2 gap-4 mb-4">
            <div>
              <label for="price" class="block text-sm font-medium text-gray-700 mb-1">Цена</label>
              <input
                type="number"
                id="price"
                value={product.price.value}
                step="0.01"
                class="w-full px-3 py-2 border border-gray-300 rounded-md"
                required
              >
            </div>
            <div>
              <label for="unit" class="block text-sm font-medium text-gray-700 mb-1">Единица измерения</label>
              <input
                type="text"
                id="unit"
                value={product.price.unit}
                class="w-full px-3 py-2 border border-gray-300 rounded-md"
                required
              >
            </div>
          </div>

          <!-- Компонент выбора атрибутов -->
          <div id="attributes-section" class="mb-4">
            <h3 class="text-lg font-medium text-gray-900 mb-3">Атрибуты товара</h3>
            <div id="attributes-container" class="space-y-4">
              <!-- Атрибуты будут добавлены динамически -->
            </div>
            <button
              type="button"
              id="add-attribute-btn"
              class="mt-3 inline-flex items-center px-3 py-2 border border-blue-300 text-sm font-medium rounded text-blue-700 bg-blue-50 hover:bg-blue-100 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Добавить атрибут
            </button>
          </div>

          <div class="mb-4">
            <label for="inStock" class="flex items-center">
              <input
                type="checkbox"
                id="inStock"
                class="h-4 w-4 text-blue-600 border-gray-300 rounded"
                checked={product.inStock}
              >
              <span class="ml-2 text-sm text-gray-700">Опубликован (доступен для просмотра на сайте)</span>
            </label>
          </div>
        </div>
      </div>

      <!-- Изображения -->
      <div class="mt-6">
        <h2 class="text-xl font-semibold mb-4">Изображения</h2>

        <div class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded">
          <div class="flex justify-between items-center">
            <div>
              <h3 class="text-sm font-medium text-blue-800 mb-2">Автоматическое определение изображений</h3>
              <p class="text-xs text-blue-700">Система автоматически найдет изображения в папке товара по новым правилам именования</p>
            </div>
            <button
              type="button"
              id="auto-detect-images"
              class="update-images-btn text-white px-3 py-1 rounded text-sm"
              style="background-color: #3b82f6;"
            >
              Обновить изображения
            </button>
          </div>
        </div>

        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-1">Главное изображение</label>
          <div class="flex items-center">
            <img
              id="current-main-image"
              src={`/product/${product.images.main}`}
              alt="Главное изображение"
              class="h-20 w-20 object-cover mr-4 rounded border"
            />
            <div>
              <input type="file" id="main-image" accept="image/*" class="text-sm mb-2">
              <p class="text-xs text-gray-500">Текущий файл: {product.images.main}</p>
            </div>
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Дополнительные изображения</label>
          <div id="additional-images-grid" class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-2">
            {product.images.additional.map((img, index) => (
              <div class="relative" data-image-path={img}>
                <img
                  src={`/product/${img}`}
                  alt={`Изображение ${index + 1}`}
                  class="h-20 w-full object-cover rounded border"
                />
                <button
                  type="button"
                  data-index={index}
                  data-image-path={img}
                  class="delete-image absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs"
                >
                  ×
                </button>
                <p class="text-xs text-gray-500 mt-1 truncate">{img.split('/').pop()}</p>
              </div>
            ))}
          </div>
          <input type="file" id="additional-images" accept="image/*" multiple class="text-sm">
          <p class="text-xs text-gray-500 mt-1">Можно добавить до 6 дополнительных изображений</p>
        </div>
      </div>

      <div class="mt-8 flex justify-end">
        <a href="/admin/products" class="bg-gray-300 text-gray-800 px-4 py-2 rounded mr-2 hover:bg-gray-400">
          Отмена
        </a>
        <button type="submit" class="save-product-btn text-white px-4 py-2 rounded" style="background-color: #3b82f6;">
          Сохранить изменения
        </button>
      </div>
    </form>
  </div>
</AdminLayout>

<style>
  /* Стили для кнопки сохранения продукта */
  .save-product-btn:hover {
    background-color: #2563eb !important;
  }

  /* Стили для кнопки обновления изображений */
  .update-images-btn:hover {
    background-color: #2563eb !important;
  }

  /* Стили для кнопки генерации SLUG */
  .generate-product-slug-btn:hover {
    background-color: #2563eb !important;
  }

  /* Стили для tooltip категории */
  .category-tooltip {
    white-space: normal;
    word-wrap: break-word;
  }

  /* Показываем tooltip при наведении на иконку или поле ввода */
  .category-info-icon:hover ~ .category-tooltip,
  .category-input:hover ~ .category-tooltip {
    opacity: 1;
    pointer-events: auto;
  }

  /* Альтернативный способ показа tooltip через JavaScript */
  .category-tooltip.show {
    opacity: 1;
    pointer-events: auto;
  }

  /* Стили для атрибутов */
  .attribute-row {
    transition: all 0.3s ease;
  }

  .attribute-row.collapsed {
    opacity: 0.9;
  }

  .collapsed-view {
    transition: all 0.2s ease;
  }

  .collapsed-view:hover {
    background-color: #e5e7eb !important;
  }
</style>

<script is:inline define:vars={{ product }}>
  // Сохраняем данные товара в глобальной переменной для использования в форме
  window.currentProduct = product;

  // Глобальные переменные для атрибутов
  window.attributeTypes = {};
  window.attributesData = {};
  let attributeCounter = 0;

  // Функция для загрузки подкатегорий
  async function loadSubcategories(categoryName, selectedSubcategory = '') {
    const subcategorySelect = document.getElementById('subcategory');

    try {
      // Загружаем данные категорий
      const response = await fetch('/data/product/categories.json');
      const categoriesData = await response.json();

      // Находим выбранную категорию
      const selectedCategory = categoriesData.categories.find(cat => cat.name === categoryName);

      if (selectedCategory && selectedCategory.subcategories) {
        // Очищаем текущие опции
        subcategorySelect.innerHTML = '<option value="">Выберите подкатегорию</option>';

        // Добавляем подкатегории
        selectedCategory.subcategories.forEach(subcategory => {
          const option = document.createElement('option');
          option.value = subcategory;
          option.textContent = subcategory;
          if (subcategory === selectedSubcategory) {
            option.selected = true;
          }
          subcategorySelect.appendChild(option);
        });

        // Включаем выбор подкатегории
        subcategorySelect.disabled = false;
      } else {
        // Если подкатегорий нет
        subcategorySelect.innerHTML = '<option value="">Подкатегории отсутствуют</option>';
        subcategorySelect.disabled = true;
      }
    } catch (error) {
      console.error('Ошибка при загрузке подкатегорий:', error);
      subcategorySelect.innerHTML = '<option value="">Ошибка загрузки подкатегорий</option>';
      subcategorySelect.disabled = true;
    }
  }

  // Инициализация подкатегорий при загрузке страницы
  document.addEventListener('DOMContentLoaded', async function() {
    const currentCategory = window.currentProduct.category;
    const currentSubcategory = window.currentProduct.subcategory;

    if (currentCategory) {
      loadSubcategories(currentCategory, currentSubcategory);
    }

    // Инициализация tooltip для категории
    initCategoryTooltip();

    // Загрузка данных атрибутов и инициализация компонента
    await loadAttributesData();
    initializeAttributesComponent();
    loadExistingAttributes();
  });

  // Функция для инициализации tooltip категории
  function initCategoryTooltip() {
    const categoryIcon = document.querySelector('.category-info-icon');
    const categoryInput = document.querySelector('.category-input');
    const tooltip = document.getElementById('category-warning-tooltip');

    if (!categoryIcon || !categoryInput || !tooltip) return;

    // Показываем tooltip при наведении на иконку
    categoryIcon.addEventListener('mouseenter', function() {
      tooltip.classList.add('show');
    });

    categoryIcon.addEventListener('mouseleave', function() {
      tooltip.classList.remove('show');
    });

    // Показываем tooltip при наведении на поле ввода
    categoryInput.addEventListener('mouseenter', function() {
      tooltip.classList.add('show');
    });

    categoryInput.addEventListener('mouseleave', function() {
      tooltip.classList.remove('show');
    });

    // Показываем tooltip при фокусе на поле ввода
    categoryInput.addEventListener('focus', function() {
      tooltip.classList.add('show');
    });

    categoryInput.addEventListener('blur', function() {
      tooltip.classList.remove('show');
    });
  }

  // Функция для загрузки данных атрибутов
  async function loadAttributesData() {
    try {
      // Загружаем конфигурацию типов атрибутов
      const typesResponse = await fetch('/api/admin/attribute-types-config');
      if (typesResponse.ok) {
        window.attributeTypes = await typesResponse.json();
      }

      // Загружаем данные атрибутов
      const dataResponse = await fetch('/data/product/attributes.json');
      if (dataResponse.ok) {
        window.attributesData = await dataResponse.json();
      }
    } catch (error) {
      console.error('Ошибка при загрузке данных атрибутов:', error);
    }
  }

  // Инициализация компонента атрибутов
  function initializeAttributesComponent() {
    const addAttributeBtn = document.getElementById('add-attribute-btn');
    if (addAttributeBtn) {
      addAttributeBtn.addEventListener('click', addAttributeRow);
    }
  }

  // Загрузка существующих атрибутов товара
  function loadExistingAttributes() {
    const product = window.currentProduct;
    if (!product || !product.attributes) return;

    // Проходим по всем атрибутам товара и создаем для них формы
    Object.entries(product.attributes).forEach(([attributeKey, attributeValue]) => {
      // Специальная обработка для размеров
      if (attributeKey === 'size') {
        // Проверяем, есть ли тип standard_sizes
        if (window.attributeTypes['standard_sizes']) {
          addAttributeRow('standard_sizes', attributeValue);
        }
        return;
      }

      // Пропускаем атрибуты, которые не имеют соответствующего типа
      if (!window.attributeTypes[attributeKey]) return;

      addAttributeRow(attributeKey, attributeValue);
    });
  }

  // Функция добавления строки атрибута
  function addAttributeRow(preselectedType = null, preselectedValue = null) {
    const container = document.getElementById('attributes-container');
    if (!container) return;

    // Сворачиваем все существующие атрибуты перед добавлением нового
    collapseAllAttributes();

    const attributeId = `attribute-${++attributeCounter}`;
    const attributeRow = document.createElement('div');
    attributeRow.className = 'attribute-row bg-gray-50 p-4 rounded-lg border';
    attributeRow.id = attributeId;

    attributeRow.innerHTML = `
      <div class="attribute-header grid grid-cols-1 md:grid-cols-12 gap-4">
        <div class="md:col-span-4">
          <label class="block text-sm font-medium text-gray-700 mb-1">Тип атрибута</label>
          <select class="attribute-type-select w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" data-attribute-id="${attributeId}">
            <option value="">Выберите тип атрибута</option>
            ${Object.entries(window.attributeTypes).map(([key, config]) =>
              `<option value="${key}" ${preselectedType === key ? 'selected' : ''}>${config.name}</option>`
            ).join('')}
          </select>
        </div>
        <div class="attribute-value-container md:col-span-6">
          <label class="block text-sm font-medium text-gray-700 mb-1">Значение</label>
          <div class="attribute-value-content">
            <p class="text-sm text-gray-500">Выберите тип атрибута</p>
          </div>
        </div>
        <div class="md:col-span-2 flex items-end justify-end">
          <button type="button" class="remove-attribute-btn px-3 py-2 bg-red-100 hover:bg-red-200 text-red-700 border border-red-300 rounded text-sm" data-attribute-id="${attributeId}">
            удалить
          </button>
        </div>
      </div>
    `;

    container.appendChild(attributeRow);

    // Добавляем обработчики событий
    const typeSelect = attributeRow.querySelector('.attribute-type-select');
    const removeBtn = attributeRow.querySelector('.remove-attribute-btn');

    if (typeSelect) {
      typeSelect.addEventListener('change', function() {
        handleAttributeTypeChange(this);
      });

      // Добавляем обработчик для сворачивания других атрибутов при фокусе
      typeSelect.addEventListener('focus', function() {
        collapseOtherAttributes(attributeId);
      });

      // Если есть предвыбранный тип, инициализируем его
      if (preselectedType) {
        handleAttributeTypeChange(typeSelect, preselectedValue);
      }
    }

    if (removeBtn) {
      removeBtn.addEventListener('click', function() {
        removeAttributeRow(this.dataset.attributeId);
      });
    }

    // Добавляем обработчик для сворачивания при клике на значения
    const valueContainer = attributeRow.querySelector('.attribute-value-container');
    if (valueContainer) {
      valueContainer.addEventListener('click', function() {
        collapseOtherAttributes(attributeId);
      });
    }

    return attributeRow;
  }

  // Функция удаления строки атрибута
  function removeAttributeRow(attributeId) {
    const row = document.getElementById(attributeId);
    if (row) {
      row.remove();
    }
  }

  // Функция сворачивания всех атрибутов
  function collapseAllAttributes() {
    const allAttributeRows = document.querySelectorAll('.attribute-row');

    allAttributeRows.forEach(row => {
      const typeSelect = row.querySelector('.attribute-type-select');
      const valueContainer = row.querySelector('.attribute-value-content');

      // Проверяем, есть ли выбранный тип и значение
      if (typeSelect && typeSelect.value && valueContainer) {
        const hasSelectedValue = checkIfHasSelectedValue(row, typeSelect.value);

        if (hasSelectedValue) {
          collapseAttributeRow(row, typeSelect.value);
        }
      }
    });
  }

  // Функция сворачивания других атрибутов
  function collapseOtherAttributes(currentAttributeId) {
    const allAttributeRows = document.querySelectorAll('.attribute-row');

    allAttributeRows.forEach(row => {
      if (row.id !== currentAttributeId) {
        const typeSelect = row.querySelector('.attribute-type-select');
        const valueContainer = row.querySelector('.attribute-value-content');

        // Проверяем, есть ли выбранный тип и значение
        if (typeSelect && typeSelect.value && valueContainer) {
          const hasSelectedValue = checkIfHasSelectedValue(row, typeSelect.value);

          if (hasSelectedValue) {
            collapseAttributeRow(row, typeSelect.value);
          }
        }
      }
    });
  }

  // Проверяем, есть ли выбранное значение в атрибуте
  function checkIfHasSelectedValue(row, attributeType) {
    if (attributeType === 'colors') {
      return row.querySelectorAll('input[type="checkbox"]:checked').length > 0;
    } else if (attributeType === 'standard_sizes') {
      const lengthInput = row.querySelector('input[name="length"]');
      const widthInput = row.querySelector('input[name="width"]');
      const heightInput = row.querySelector('input[name="height"]');
      return (lengthInput && lengthInput.value) || (widthInput && widthInput.value) || (heightInput && heightInput.value);
    } else {
      const select = row.querySelector('.attribute-value-select');
      return select && select.value;
    }
  }

  // Сворачиваем конкретную строку атрибута
  function collapseAttributeRow(row, attributeType) {
    const valueContainer = row.querySelector('.attribute-value-content');
    if (!valueContainer) return;

    // Получаем выбранные значения для отображения
    const selectedValues = getSelectedValuesText(row, attributeType);

    if (selectedValues) {
      valueContainer.innerHTML = `
        <div class="collapsed-view p-3 bg-gray-100 rounded border cursor-pointer" onclick="expandAttributeRow('${row.id}')">
          <div class="text-sm font-semibold text-gray-800">${selectedValues}</div>
          <div class="text-xs text-gray-500 mt-1">Нажмите для редактирования</div>
        </div>
      `;

      // Добавляем класс для отслеживания состояния
      row.classList.add('collapsed');
    }
  }

  // Получаем текст выбранных значений
  function getSelectedValuesText(row, attributeType) {
    if (attributeType === 'colors') {
      const checked = row.querySelectorAll('input[type="checkbox"]:checked');
      return Array.from(checked).map(cb => cb.value).join(', ');
    } else if (attributeType === 'standard_sizes') {
      const lengthInput = row.querySelector('input[name="length"]');
      const widthInput = row.querySelector('input[name="width"]');
      const heightInput = row.querySelector('input[name="height"]');

      const length = lengthInput?.value || 0;
      const width = widthInput?.value || 0;
      const height = heightInput?.value || 0;

      if (length || width || height) {
        return `${length}×${width}×${height} мм`;
      }
    } else if (attributeType === 'weight') {
      const select = row.querySelector('.attribute-value-select');
      if (select && select.value) {
        try {
          const weight = JSON.parse(select.value.replace(/&quot;/g, '"'));
          return `${weight.value} ${weight.unit}`;
        } catch {
          return select.selectedOptions[0]?.textContent || '';
        }
      }
    } else {
      const select = row.querySelector('.attribute-value-select');
      if (select && select.value) {
        const selectedOption = select.selectedOptions[0];
        return selectedOption?.textContent || '';
      }
    }
    return null;
  }

  // Разворачиваем атрибут (глобальная функция для onclick)
  window.expandAttributeRow = function(attributeId) {
    const row = document.getElementById(attributeId);
    if (!row) return;

    const typeSelect = row.querySelector('.attribute-type-select');
    if (typeSelect && typeSelect.value) {
      // Восстанавливаем полный интерфейс
      handleAttributeTypeChange(typeSelect);
      row.classList.remove('collapsed');

      // Сворачиваем другие атрибуты
      collapseOtherAttributes(attributeId);
    }
  };

  // Обработчик изменения типа атрибута
  function handleAttributeTypeChange(selectElement, preselectedValue = null) {
    const attributeType = selectElement.value;
    const attributeId = selectElement.dataset.attributeId;
    const row = document.getElementById(attributeId);

    if (!row || !attributeType) return;

    const valueContainer = row.querySelector('.attribute-value-content');
    if (!valueContainer) return;

    const typeConfig = window.attributeTypes[attributeType];
    const attributeData = window.attributesData[attributeType];

    if (!typeConfig || !attributeData) {
      valueContainer.innerHTML = '<p class="text-sm text-red-500">Ошибка загрузки данных атрибута</p>';
      return;
    }

    // Генерируем интерфейс в зависимости от типа атрибута
    generateAttributeInterface(valueContainer, attributeType, typeConfig, attributeData, preselectedValue);
  }

  // Функция генерации интерфейса атрибута
  function generateAttributeInterface(valueContainer, attributeType, typeConfig, attributeData, preselectedValue) {
    if (typeConfig.isSimpleArray) {
      // Простой массив строк (например, текстуры)
      const selectedValues = Array.isArray(preselectedValue) ? preselectedValue : (preselectedValue ? [preselectedValue] : []);
      valueContainer.innerHTML = `
        <select class="attribute-value-select w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" multiple>
          ${attributeData.map(item => `<option value="${item}" ${selectedValues.includes(item) ? 'selected' : ''}>${item}</option>`).join('')}
        </select>
        <p class="text-xs text-gray-500 mt-1">Удерживайте Ctrl для выбора нескольких значений</p>
      `;
    } else if (attributeType === 'colors') {
      // Специальная обработка для цветов
      const selectedColors = Array.isArray(preselectedValue) ? preselectedValue : (preselectedValue ? [preselectedValue] : []);
      valueContainer.innerHTML = `
        <div class="space-y-2">
          ${attributeData.map(color => `
            <label class="flex items-center">
              <input type="checkbox" value="${color.name}" ${selectedColors.includes(color.name) ? 'checked' : ''} class="attribute-checkbox mr-2" data-name="${color.name}">
              <div class="w-4 h-4 rounded border mr-2" style="background-color: ${color.hex}"></div>
              <span>${color.name}</span>
            </label>
          `).join('')}
        </div>
      `;
    } else if (attributeType === 'standard_sizes') {
      // Специальная обработка для размеров
      const selectedSize = preselectedValue || {};
      valueContainer.innerHTML = `
        <div class="grid grid-cols-3 gap-2">
          <div>
            <label class="block text-xs text-gray-500">Длина (мм)</label>
            <input type="number" name="length" value="${selectedSize.length || ''}" class="w-full px-2 py-1 border border-gray-300 rounded text-sm">
          </div>
          <div>
            <label class="block text-xs text-gray-500">Ширина (мм)</label>
            <input type="number" name="width" value="${selectedSize.width || ''}" class="w-full px-2 py-1 border border-gray-300 rounded text-sm">
          </div>
          <div>
            <label class="block text-xs text-gray-500">Высота (мм)</label>
            <input type="number" name="height" value="${selectedSize.height || ''}" class="w-full px-2 py-1 border border-gray-300 rounded text-sm">
          </div>
        </div>
      `;
    } else if (attributeType === 'weight') {
      // Специальная обработка для веса
      const selectedWeight = preselectedValue || '';
      valueContainer.innerHTML = `
        <select class="attribute-value-select w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
          <option value="">Выберите вес</option>
          ${attributeData.map(item => {
            const itemValue = JSON.stringify(item);
            const isSelected = selectedWeight === item.value || (typeof selectedWeight === 'object' && selectedWeight.value === item.value);
            return `<option value="${itemValue.replace(/"/g, '&quot;')}" ${isSelected ? 'selected' : ''}>${item.value} ${item.unit}</option>`;
          }).join('')}
        </select>
      `;
    } else if (['surfaces', 'patterns', 'color_pigments'].includes(attributeType)) {
      // Атрибуты с id, name и description
      const selectedId = typeof preselectedValue === 'object' ? preselectedValue.id : preselectedValue;
      valueContainer.innerHTML = `
        <select class="attribute-value-select w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
          <option value="">Выберите ${window.attributeTypes[attributeType].name.toLowerCase()}</option>
          ${attributeData.map(item => `<option value="${JSON.stringify(item).replace(/"/g, '&quot;')}" ${selectedId === item.id ? 'selected' : ''}>${item.name} - ${item.description}</option>`).join('')}
        </select>
      `;
    } else {
      // Обработка пользовательских типов атрибутов
      valueContainer.innerHTML = `
        <select class="attribute-value-select w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
          <option value="">Выберите значение</option>
          ${attributeData.map(item => {
            const itemValue = typeof item === 'object' ? JSON.stringify(item) : item;
            const displayValue = typeof item === 'object' ? (item.name || item.value || JSON.stringify(item)) : item;
            const isSelected = JSON.stringify(preselectedValue) === JSON.stringify(item);
            return `<option value="${itemValue.replace(/"/g, '&quot;')}" ${isSelected ? 'selected' : ''}>${displayValue}</option>`;
          }).join('')}
        </select>
      `;
    }
  }

  // Функция сбора данных атрибутов из форм
  function collectAttributesData() {
    const attributes = {};

    document.querySelectorAll('.attribute-row').forEach(row => {
      const typeSelect = row.querySelector('.attribute-type-select');
      const attributeType = typeSelect?.value;

      if (!attributeType) return;

      const valueContainer = row.querySelector('.attribute-value-content');
      if (!valueContainer) return;

      let attributeValue = null;

      if (attributeType === 'colors') {
        // Собираем выбранные цвета из чекбоксов
        const checkedColors = valueContainer.querySelectorAll('input[type="checkbox"]:checked');
        attributeValue = Array.from(checkedColors).map(cb => cb.value);
      } else if (attributeType === 'standard_sizes') {
        // Собираем размеры из полей ввода
        const lengthInput = valueContainer.querySelector('input[name="length"]');
        const widthInput = valueContainer.querySelector('input[name="width"]');
        const heightInput = valueContainer.querySelector('input[name="height"]');

        attributeValue = {
          length: parseInt(lengthInput?.value) || 0,
          width: parseInt(widthInput?.value) || 0,
          height: parseInt(heightInput?.value) || 0
        };
      } else {
        // Для остальных типов атрибутов
        const select = valueContainer.querySelector('.attribute-value-select');
        if (select) {
          if (select.multiple) {
            // Множественный выбор
            attributeValue = Array.from(select.selectedOptions).map(option => option.value);
          } else {
            // Одиночный выбор
            const selectedValue = select.value;
            if (selectedValue) {
              try {
                // Пытаемся распарсить JSON для сложных объектов
                attributeValue = JSON.parse(selectedValue);
              } catch {
                // Если не JSON, используем как строку
                attributeValue = selectedValue;
              }
            }
          }
        }
      }

      if (attributeValue !== null && attributeValue !== undefined) {
        // Для размеров используем специальный ключ 'size'
        if (attributeType === 'standard_sizes') {
          attributes.size = attributeValue;
        } else {
          attributes[attributeType] = attributeValue;
        }
      }
    });

    return attributes;
  }

  // Обработчик отправки формы
  document.getElementById('product-form').addEventListener('submit', async (e) => {
    e.preventDefault();

    // Сбор данных формы
    const productId = document.getElementById('product-id').value;
    const name = document.getElementById('name').value;
    const slug = document.getElementById('product-slug').value;
    const category = document.getElementById('category').value;
    const subcategory = document.getElementById('subcategory').value;
    const shortDescription = document.getElementById('shortDescription').value;
    const fullDescription = document.getElementById('fullDescription').value;
    const price = parseFloat(document.getElementById('price').value);
    const unit = document.getElementById('unit').value;
    // Сбор данных атрибутов из динамических форм
    const attributes = collectAttributesData();
    const inStock = document.getElementById('inStock').checked;

    // Получаем текущие данные товара из глобальной переменной
    const currentProduct = window.currentProduct;

    // Получение правильного categorySlug из данных категорий
    // Загружаем данные категорий для получения правильного slug
    let categorySlug = category.toLowerCase().replace(/\s+/g, '-');
    try {
      const categoriesResponse = await fetch('/data/product/categories.json');
      const categoriesData = await categoriesResponse.json();
      const foundCategory = categoriesData.categories.find(cat => cat.name === category);
      if (foundCategory) {
        categorySlug = foundCategory.slug;
      }
    } catch (error) {
      console.warn('Не удалось загрузить данные категорий, используется fallback slug:', error);
    }

    // Обновляем данные изображений перед сохранением
    updateProductImages();

    // Формирование объекта товара
    const productData = {
      id: productId,
      name,
      slug,
      category,
      categorySlug,
      subcategory,
      shortDescription,
      fullDescription,
      price: {
        value: price,
        unit
      },
      attributes: attributes,
      images: window.currentProduct.images, // Используем актуальные данные изображений
      inStock,
      popularity: currentProduct.popularity || 4.0
    };

    try {
      console.log('Отправка данных на сервер:', productData);

      // Отправка данных на сервер
      const response = await fetch('/api/admin/products', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(productData)
      });

      console.log('Ответ сервера:', response.status, response.statusText);

      if (response.ok) {
        const result = await response.json();
        console.log('Результат:', result);
        await window.adminModal?.showSuccess('Товар успешно обновлен!');
        window.location.href = '/admin/products';
      } else {
        const errorData = await response.json();
        console.error('Ошибка сервера:', errorData);
        await window.adminModal?.showError('Ошибка при обновлении товара: ' + (errorData.error || 'Неизвестная ошибка'));
      }
    } catch (error) {
      console.error('Ошибка:', error);
      alert('Ошибка при обновлении товара: ' + error.message);
    }
  });

  // Обработчик автоматического определения изображений
  document.getElementById('auto-detect-images').addEventListener('click', async () => {
    const productId = document.getElementById('product-id').value;

    try {
      const response = await fetch(`/api/admin/detect-images?productId=${productId}`);
      const result = await response.json();

      if (result.success && result.images) {
        // Обновляем главное изображение
        if (result.images.main) {
          const mainImg = document.getElementById('current-main-image');
          mainImg.src = `/product/${result.images.main}`;

          // Обновляем текст с именем файла
          const mainImageContainer = mainImg.parentElement.parentElement;
          const fileNameText = mainImageContainer.querySelector('p.text-xs');
          if (fileNameText) {
            fileNameText.textContent = `Текущий файл: ${result.images.main}`;
          }
        }

        // Обновляем дополнительные изображения
        const additionalGrid = document.getElementById('additional-images-grid');
        additionalGrid.innerHTML = '';

        result.images.additional.forEach((img, index) => {
          const imgContainer = document.createElement('div');
          imgContainer.className = 'relative';
          imgContainer.setAttribute('data-image-path', img);

          imgContainer.innerHTML = `
            <img
              src="/product/${img}"
              alt="Изображение ${index + 1}"
              class="h-20 w-full object-cover rounded border"
            />
            <button
              type="button"
              data-index="${index}"
              data-image-path="${img}"
              class="delete-image absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs"
            >
              ×
            </button>
            <p class="text-xs text-gray-500 mt-1 truncate">${img.split('/').pop()}</p>
          `;

          additionalGrid.appendChild(imgContainer);
        });

        // Переназначаем обработчики удаления
        attachDeleteHandlers();

        await window.adminModal?.showSuccess('Изображения успешно обновлены!');
      } else {
        await window.adminModal?.showError('Не удалось определить изображения: ' + (result.error || 'Неизвестная ошибка'));
      }
    } catch (error) {
      console.error('Ошибка при определении изображений:', error);
      await window.adminModal?.showError('Произошла ошибка при определении изображений. Попробуйте еще раз.');
    }
  });

  // Функция для назначения обработчиков удаления изображений
  function attachDeleteHandlers() {
    document.querySelectorAll('.delete-image').forEach(button => {
      button.addEventListener('click', async () => {
        const confirmed = await window.confirmModal?.show({
          title: 'Подтверждение удаления',
          message: 'Вы уверены, что хотите удалить это изображение?',
          confirmText: 'Удалить',
          cancelText: 'Отмена',
          confirmButtonClass: 'bg-red-100 hover:bg-red-200 text-red-700 border border-red-300'
        });

        if (confirmed) {
          const imagePath = button.getAttribute('data-image-path');
          const container = button.closest('.relative');

          try {
            // Удаляем файл с сервера
            const response = await fetch(`/api/admin/delete-image?imagePath=${encodeURIComponent(imagePath)}`, {
              method: 'DELETE'
            });

            const result = await response.json();

            if (result.success) {
              // Удаляем элемент из DOM
              container.remove();

              // Обновляем данные товара
              updateProductImages();

              console.log('Изображение успешно удалено:', imagePath);
            } else {
              alert('Ошибка при удалении изображения: ' + (result.error || 'Неизвестная ошибка'));
            }
          } catch (error) {
            console.error('Ошибка при удалении изображения:', error);
            alert('Ошибка при удалении изображения');
          }
        }
      });
    });
  }

  // Обработчик загрузки главного изображения
  document.getElementById('main-image').addEventListener('change', async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const productId = document.getElementById('product-id').value;
    const formData = new FormData();
    formData.append('file', file);
    formData.append('productId', productId);
    formData.append('imageType', 'main');

    try {
      const response = await fetch('/api/admin/upload-image', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (result.success) {
        // Обновляем главное изображение
        const mainImg = document.getElementById('current-main-image');
        mainImg.src = `/product/${result.imagePath}?t=${Date.now()}`;

        // Обновляем текст с именем файла
        const fileNameText = mainImg.parentElement.parentElement.querySelector('p.text-xs');
        if (fileNameText) {
          fileNameText.textContent = `Текущий файл: ${result.imagePath}`;
        }

        // Обновляем данные товара
        window.currentProduct.images.main = result.imagePath;

        alert('Главное изображение успешно загружено!');
      } else {
        alert('Ошибка при загрузке изображения: ' + (result.error || 'Неизвестная ошибка'));
      }
    } catch (error) {
      console.error('Ошибка при загрузке изображения:', error);
      alert('Ошибка при загрузке изображения');
    }

    // Очищаем input
    event.target.value = '';
  });

  // Обработчик загрузки дополнительных изображений
  document.getElementById('additional-images').addEventListener('change', async (event) => {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    const productId = document.getElementById('product-id').value;

    for (const file of files) {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('productId', productId);
      formData.append('imageType', 'additional');

      try {
        const response = await fetch('/api/admin/upload-image', {
          method: 'POST',
          body: formData
        });

        const result = await response.json();

        if (result.success) {
          // Добавляем новое изображение в сетку
          addImageToGrid(result.imagePath);

          // Обновляем данные товара
          if (!window.currentProduct.images.additional) {
            window.currentProduct.images.additional = [];
          }
          window.currentProduct.images.additional.push(result.imagePath);

          console.log('Дополнительное изображение загружено:', result.imagePath);
        } else {
          alert('Ошибка при загрузке изображения ' + file.name + ': ' + (result.error || 'Неизвестная ошибка'));
        }
      } catch (error) {
        console.error('Ошибка при загрузке изображения:', error);
        alert('Ошибка при загрузке изображения ' + file.name);
      }
    }

    // Очищаем input
    event.target.value = '';
    alert('Загрузка дополнительных изображений завершена!');
  });

  // Функция для добавления изображения в сетку
  function addImageToGrid(imagePath) {
    const grid = document.getElementById('additional-images-grid');
    const index = grid.children.length;

    const imgContainer = document.createElement('div');
    imgContainer.className = 'relative';
    imgContainer.setAttribute('data-image-path', imagePath);

    imgContainer.innerHTML = `
      <img
        src="/product/${imagePath}?t=${Date.now()}"
        alt="Изображение ${index + 1}"
        class="h-20 w-full object-cover rounded border"
      />
      <button
        type="button"
        data-index="${index}"
        data-image-path="${imagePath}"
        class="delete-image absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs"
      >
        ×
      </button>
      <p class="text-xs text-gray-500 mt-1 truncate">${imagePath.split('/').pop()}</p>
    `;

    grid.appendChild(imgContainer);

    // Переназначаем обработчики удаления
    attachDeleteHandlers();
  }

  // Функция для обновления данных изображений товара
  function updateProductImages() {
    const mainImg = document.getElementById('current-main-image');
    const mainImagePath = mainImg.src.split('/product/')[1]?.split('?')[0];

    if (mainImagePath) {
      window.currentProduct.images.main = mainImagePath;
    }

    // Собираем дополнительные изображения из DOM
    const additionalImages = [];
    document.querySelectorAll('#additional-images-grid .relative').forEach(container => {
      const imagePath = container.getAttribute('data-image-path');
      if (imagePath) {
        additionalImages.push(imagePath);
      }
    });

    window.currentProduct.images.additional = additionalImages;
  }

  // Инициализируем обработчики удаления при загрузке страницы
  attachDeleteHandlers();

  // Функция генерации SLUG с транслитерацией
  function generateSlugFromName(name) {
    if (!name) return '';

    // Маппинг кириллических символов в латинские для SLUG
    const cyrillicToLatin = {
      'А': 'a', 'Б': 'b', 'В': 'v', 'Г': 'g', 'Д': 'd', 'Е': 'e', 'Ё': 'yo', 'Ж': 'zh',
      'З': 'z', 'И': 'i', 'Й': 'y', 'К': 'k', 'Л': 'l', 'М': 'm', 'Н': 'n', 'О': 'o',
      'П': 'p', 'Р': 'r', 'С': 's', 'Т': 't', 'У': 'u', 'Ф': 'f', 'Х': 'h', 'Ц': 'ts',
      'Ч': 'ch', 'Ш': 'sh', 'Щ': 'sch', 'Ъ': '', 'Ы': 'y', 'Ь': '', 'Э': 'e', 'Ю': 'yu', 'Я': 'ya',
      'а': 'a', 'б': 'b', 'в': 'v', 'г': 'g', 'д': 'd', 'е': 'e', 'ё': 'yo', 'ж': 'zh',
      'з': 'z', 'и': 'i', 'й': 'y', 'к': 'k', 'л': 'l', 'м': 'm', 'н': 'n', 'о': 'o',
      'п': 'p', 'р': 'r', 'с': 's', 'т': 't', 'у': 'u', 'ф': 'f', 'х': 'h', 'ц': 'ts',
      'ч': 'ch', 'ш': 'sh', 'щ': 'sch', 'ъ': '', 'ы': 'y', 'ь': '', 'э': 'e', 'ю': 'yu', 'я': 'ya'
    };

    // Транслитерация кириллицы в латиницу
    let transliterated = '';
    for (let i = 0; i < name.length; i++) {
      const char = name[i];
      transliterated += cyrillicToLatin[char] || char;
    }

    // Создание SLUG: приведение к нижнему регистру, замена пробелов на дефисы, удаление недопустимых символов
    return transliterated
      .toLowerCase()
      .replace(/\s+/g, '-')           // Заменяем пробелы на дефисы
      .replace(/[^a-z0-9-]/g, '')     // Удаляем все символы кроме букв, цифр и дефисов
      .replace(/--+/g, '-')           // Заменяем множественные дефисы на одинарные
      .replace(/^-+/g, '')            // Удаляем дефисы в начале
      .replace(/-+$/g, '');           // Удаляем дефисы в конце
  }

  // Обработчик кнопки генерации SLUG
  document.getElementById('generate-product-slug-btn').addEventListener('click', function() {
    const nameInput = document.getElementById('name');
    const productSlugInput = document.getElementById('product-slug');

    if (nameInput.value) {
      const slug = generateSlugFromName(nameInput.value);
      productSlugInput.value = slug;
    }
  });
</script>
